import {
  Body,
  Controller,
  HttpException,
  HttpStatus,
  HttpCode,
  Post,
  Get,
  Param,
  UseFilters,
  Res,
  Request,
  NotFoundException,
  BadRequestException,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { AdminAuthService } from './adminAuth.service';
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AllExceptionsFilter } from 'src/expcetions-filter';
import { LogInDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { LoginResponseDto } from './dto/login-response.dto';
import { Response } from 'express';
import { FRONTEND_ROUTES } from './constants';
import { ConfigService } from '@nestjs/config';
import {
  ForgotPasswordDto,
  ResetPasswordDto,
} from './dto/forgot-reset-password.dto';
import { ResendVerificationDto } from './dto/resend-verification.dto';
import { AuthGuard } from '../auth_admin/adminAuth.guard';
import { AdminActivitiesService } from '../admin_activities/adminActivities.service';
import { AdminUsersResponseDto } from './dto/admin-user-response.dto';

@UseFilters(AllExceptionsFilter)
@Controller('auth_admin')
@ApiTags('auth_admin')
export class AdminAuthController {
  constructor(
    private adminAuthService: AdminAuthService,
    private readonly configService: ConfigService,
    private readonly adminActivitiesService: AdminActivitiesService,
  ) {}

  @HttpCode(HttpStatus.OK)
  @Post('/login')
  @ApiOperation({
    summary: 'User Login',
    description: 'Login user with existing account.',
  })
  @ApiBody({
    description: 'The user login information',
    type: LogInDto,
  })
  @ApiResponse({
    status: 201,
    description: 'The user has been successfully logged in.',
    type: LoginResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'A user with this email not exists.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error.',
  })
  async login(@Body() logInDto: LogInDto): Promise<LoginResponseDto> {
    try {
      return await this.adminAuthService.login(logInDto);
    } catch (error) {
      let status = HttpStatus.INTERNAL_SERVER_ERROR;
      if (error.message.includes('UUID already exists')) {
        status = HttpStatus.BAD_REQUEST;
      }
      throw new HttpException(
        {
          status: status,
          error: error.message,
        },
        status,
      );
    }
  }

  @Post('/register')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Register a new admin user' })
  @ApiResponse({
    status: 201,
    description: 'The admin has been successfully registered.',
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 409, description: 'Email already in use.' })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  async registerAdmin(@Body() registerDto: RegisterDto, @Request() req) {
    try {
      const result = await this.adminAuthService.registerAdmin(
        registerDto.email,
        registerDto.password,
        registerDto.name,
        registerDto.role,
      );
      await this.adminActivitiesService.logActivity({
        admin_id: req.user.id, // ID of the admin performing the registration
        action: 'REGISTER_ADMIN',
        entity: 'admin',
        entity_id: result.id.toString(), // ID of the newly registered admin
        details: {
          registered_email: registerDto.email,
          registered_name: registerDto.name,
          assigned_role: registerDto.role,
          created_at: new Date().toISOString(),
        },
      });
      return result;
    } catch (error) {
      if (req.user?.id) {
        await this.adminActivitiesService.logActivity({
          admin_id: req.user.id,
          action: 'REGISTER_ADMIN_FAILED',
          entity: 'admin',
          details: {
            attempted_email: registerDto.email,
            attempted_name: registerDto.name,
            attempted_role: registerDto.role,
            error: error.message,
            timestamp: new Date().toISOString(),
          },
        });
      }
      if (error instanceof HttpException) {
        throw error;
      }

      if (error.code === 'P2002') {
        throw new HttpException(
          {
            status: HttpStatus.CONFLICT,
            error: 'Email already in use',
          },
          HttpStatus.CONFLICT,
        );
      }
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: 'An unexpected error occurred during registration',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('verify/:token')
  async verifyEmail(@Param('token') token: string, @Res() res: Response) {
    try {
      await this.adminAuthService.verifyEmail(token);
      // Redirect to frontend success page
      const frontendUrl = this.configService.get<string>('FRONTEND_URL');
      const successUrl = `${frontendUrl}${FRONTEND_ROUTES.EMAIL_VERIFICATION_SUCCESS}`;
      return res.redirect(successUrl);
    } catch (error) {
      // Redirect to frontend error page
      const frontendUrl = this.configService.get<string>('FRONTEND_URL');
      const errorUrl = `${frontendUrl}${
        FRONTEND_ROUTES.EMAIL_VERIFICATION_ERROR
      }?error=${encodeURIComponent(error.message)}`;
      return res.redirect(errorUrl);
    }
  }

  @Post('/resend-verification')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Resend verification email' })
  @ApiResponse({
    status: 200,
    description: 'Verification email sent successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        success: { type: 'boolean' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        success: { type: 'boolean' },
        error: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async resendVerificationEmail(
    @Body() resendVerificationDto: ResendVerificationDto,
  ) {
    try {
      await this.adminAuthService.resendVerificationEmail(
        resendVerificationDto.email,
      );
      return {
        success: true,
        message: 'Verification email has been resent successfully',
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(
          {
            success: false,
            message: 'User not found',
            error: 'No user found with this email address',
          },
          HttpStatus.NOT_FOUND,
        );
      }
      if (error instanceof BadRequestException) {
        throw new HttpException(
          {
            success: false,
            message: 'Invalid request',
            error: error.message,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
      throw new HttpException(
        {
          success: false,
          message: 'Internal server error',
          error: 'An unexpected error occurred',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('/forgot-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Request password reset' })
  @ApiResponse({
    status: 200,
    description: 'Reset password email sent successfully',
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    try {
      await this.adminAuthService.createPasswordResetToken(
        forgotPasswordDto.email,
      );
    } catch (error) {
      if (error instanceof NotFoundException) {
        // Still return success to prevent email enumeration
        return {
          message:
            'If an account exists with this email, a password reset link has been sent.',
        };
      }
      throw error;
    }
    return {
      message:
        'If an account exists with this email, a password reset link has been sent.',
    };
  }

  @Post('/reset-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Reset password with token' })
  @ApiResponse({ status: 200, description: 'Password reset successfully' })
  @ApiResponse({ status: 400, description: 'Invalid or expired token' })
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    try {
      await this.adminAuthService.resetPassword(
        resetPasswordDto.token,
        resetPasswordDto.newPassword,
      );
      return { message: 'Password reset successfully' };
    } catch (error) {
      console.error('Error in resetPassword:', error);

      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: 'An unexpected error occurred during password reset',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/admin-users')
  @UseGuards(AuthGuard)
  @ApiOperation({
    summary: 'Get admin users for assignment',
    description:
      'Retrieves list of admin users that can be assigned todos. Only accessible by ADMIN, SUPERADMIN, or DEVELOPER roles.',
  })
  @ApiResponse({
    status: 200,
    description: 'Admin users retrieved successfully',
    type: AdminUsersResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - insufficient permissions',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getAdminUsers(@Request() req: any): Promise<AdminUsersResponseDto> {
    try {
      const userRole = req.user?.role;
      if (!userRole) {
        throw new HttpException(
          'User role not found in request',
          HttpStatus.UNAUTHORIZED,
        );
      }

      const adminUsers = await this.adminAuthService.getAdminUsers(userRole);

      // Log the activity
      await this.adminActivitiesService.logActivity({
        admin_id: req.user?.id,
        action: 'GET_ADMIN_USERS',
        entity: 'admin_users',
        details: { count: adminUsers.length },
        ip_address: req.ip,
      });

      return {
        success: true,
        data: adminUsers,
        message: 'Admin users retrieved successfully',
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw new HttpException(
          {
            success: false,
            message: 'Insufficient permissions',
            error: error.message,
          },
          HttpStatus.UNAUTHORIZED,
        );
      }
      throw new HttpException(
        {
          success: false,
          message: 'Failed to retrieve admin users',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
