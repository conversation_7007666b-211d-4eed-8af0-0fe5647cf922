generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "darwin-arm64", "linux-arm64-openssl-1.1.x"]
}

datasource db {
  provider          = "mysql"
  url               = env("DATABASE_URL")
  shadowDatabaseUrl = env("DATABASE_SHADOW_URL")
}

model approval_emails {
  id                                        Int       @id @default(autoincrement())
  approved                                  Boolean?
  approvedOn                                DateTime? @db.DateTime(0)
  emailTriggeredOn                          DateTime? @db.DateTime(0)
  firstEmailTriggeredOn                     DateTime? @db.DateTime(0)
  retriggerCounter                          Int?
  childUserId                               Int?
  guardianUserId                            Int?
  user_approval_emails_childUserIdTouser    user?     @relation("approval_emails_childUserIdTouser", fields: [childUserId], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "approval_emails_ibfk_1")
  user_approval_emails_guardianUserIdTouser user?     @relation("approval_emails_guardianUserIdTouser", fields: [guardianUserId], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "approval_emails_ibfk_2")

  @@index([childUserId], map: "childUserId")
  @@index([guardianUserId], map: "guardianUserId")
}

model calibration {
  id               Int                @id @default(autoincrement())
  user_id          Int?
  calibration_date DateTime?          @db.Date
  deviceId         Int?            @map("device_id") 
  user             user?              @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "calibration_ibfk_1")
  calibration_data calibration_data[]
  
  @@index([user_id], map: "user_id")
}



model calibration_data {
  id             Int          @id @default(autoincrement())
  value          Decimal?     @db.Decimal(20, 15)
  calibration_id Int?
  calibration    calibration? @relation(fields: [calibration_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "calibration_data_ibfk_1")

  @@index([calibration_id], map: "calibration_id")
}


model comments {
  id         Int      @id @default(autoincrement())
  user_id    Int
  text       String   @db.Text
  created_on DateTime @db.DateTime(0)
  user       user     @relation(fields: [user_id], references: [id], onUpdate: Restrict, map: "comments_ibfk_1")

  @@index([user_id], map: "user_id")
}

model device_data {
  id             Int       @id @default(autoincrement())
  userId         Int?      @map("user_id")
  user           user?     @relation("UserDevice", fields: [userId], references: [id])
  deviceId       String    @db.VarChar(255)
  deviceOffset   Float?
  height         Float?
  pixelDensity   Float?
  width          Float?
  mmfor1Pixel    Decimal?  @db.Decimal(5, 2)
  model          String?   @db.VarChar(255)
  os             String?   @db.VarChar(255)
  pixelfor1mm    Decimal?  @db.Decimal(5, 2)
  diagonalLength Decimal?  @db.Decimal(8, 2)
  deviceChecksum String?   @map("device_checksum") @db.VarChar(64)
  
  trainingSessions training_session_data[] @relation("DeviceTrainingSessions")
}




model mapcog_legPers {
  dateAdded      String?          @db.VarChar(30)
  username       String           @id @db.VarChar(50)
  password       String?          @db.VarChar(50)
  logindef       String?          @db.VarChar(50)
  mapcog_patient mapcog_patient[]
  mapcog_test    mapcog_test[]
}

model mapcog_patient {
  legPersId      String?         @db.VarChar(50)
  sent           String?         @db.VarChar(1)
  patientId      String          @id @db.VarChar(50)
  firstName      String?         @db.Text
  lastName       String?         @db.Text
  age            Int?
  gender         String?         @db.VarChar(1)
  dateAdded      String?         @db.VarChar(30)
  mapcog_legPers mapcog_legPers? @relation(fields: [legPersId], references: [username], onDelete: Restrict, onUpdate: Restrict, map: "mapcog_patient_ibfk_1")
  mapcog_test    mapcog_test[]

  @@index([legPersId], map: "legPersId")
}

model mapcog_pause {
  id          Int           @id @default(autoincrement())
  resultId    Int
  pauseTime   Int?
  pauseLength Int?
  result      mapcog_result @relation(fields: [resultId], references: [id])

  @@index([resultId], map: "resultId")
}

model mapcog_result {
  orderIndex Int?
  duration   Int?
  id         Int            @id @default(autoincrement())
  testId     Int
  pauses     mapcog_pause[]
  test       mapcog_test    @relation(fields: [testId], references: [id])

  @@index([testId], map: "testId")
}

model mapcog_test {
  sent           String?         @db.VarChar(1)
  id             Int             @id @default(autoincrement())
  dateAdded      String?         @db.VarChar(30)
  legPersId      String?         @db.VarChar(50)
  patientId      String?         @db.VarChar(50)
  medicine       String?         @db.Text
  dose           String?         @db.Text
  diagnosis      String?         @db.Text
  comments       String?         @db.Text
  results        mapcog_result[]
  mapcog_legPers mapcog_legPers? @relation(fields: [legPersId], references: [username], onDelete: Restrict, onUpdate: Restrict, map: "mapcog_test_ibfk_1")
  mapcog_patient mapcog_patient? @relation(fields: [patientId], references: [patientId], onDelete: Restrict, onUpdate: Restrict, map: "mapcog_test_ibfk_2")

  @@index([legPersId], map: "legPersId")
  @@index([patientId], map: "patientId")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model reading_chain {
  ID Int

  @@index([ID], map: "ID")
  @@ignore
}

model scheduled_notifications {
  id                 Int     @id @default(autoincrement())
  user_id            Int?
  title              String  @db.VarChar(255)
  body               String  @db.VarChar(255)
  scheduled_datetime String? @db.VarChar(255)
  status             String? @db.VarChar(255)
}

model scheduled_test {
  id                  Int       @id @default(autoincrement())
  user_id             Int?
  test_id             Int?
  scheduled_datetime  DateTime? @db.Timestamp(0)
  completion_datetime DateTime? @db.Timestamp(0)
  created_at          DateTime? @db.Timestamp(0)
  updated_at          DateTime? @db.Timestamp(0)
  is_mandatory        Boolean?
  notice_period       Int?      @default(3)
  user                user?     @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "scheduled_test_ibfk_1")
  test                test?     @relation(fields: [test_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "scheduled_test_ibfk_2")

  @@index([user_id], map: "scheduled_test_ibfk_1")
  @@index([test_id], map: "scheduled_test_ibfk_2")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model sub_admin_assigned_users {
  subAdminId     Int?
  userId         Int?
  sub_admin_data sub_admin_data? @relation(fields: [subAdminId], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "sub_admin_assigned_users_ibfk_1")
  user           user?           @relation(fields: [userId], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "sub_admin_assigned_users_ibfk_2")

  @@index([subAdminId], map: "subAdminId")
  @@index([userId], map: "userId")
  @@ignore
}

model sub_admin_data {
  id                       Int                        @id @default(autoincrement())
  Email                    String                     @db.VarChar(255)
  FirstName                String?                    @db.VarChar(255)
  LastName                 String?                    @db.VarChar(255)
  MobileNumber             String?                    @db.VarChar(255)
  sub_admin_assigned_users sub_admin_assigned_users[] @ignore
}

model test {
  id             Int              @id @default(autoincrement())
  name           String?          @db.VarChar(255)
  name_sv        String?          @db.VarChar(255)
  created_at     DateTime         @db.Timestamp(0)
  updated_at     DateTime         @db.Timestamp(0)
  is_mandatory   Boolean          @default(false)
  notice_period  Int?             @default(3)
  color          String           @db.VarChar(255)
  active         Boolean?         @default(true)
  scheduled_test scheduled_test[]
}

model training_session {
  id               Int       @id @default(autoincrement())
  user_id          Int?
  start_datetime   DateTime? @db.DateTime(0)
  end_datetime     DateTime? @db.DateTime(0)
  type             Int?
  source           Int?
  offset           Float?
  oscillation_time Int?
  pendulum_length  Float?
  speed            Float?
  duration         Float?
  created_at       DateTime? @default(now()) @db.Timestamp(0)
  updated_at       DateTime? @default(now()) @db.Timestamp(0)
  user             user?     @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "training_session_ibfk_1")

  @@index([user_id], map: "user_id")
}

model user {
  id                       Int                        @id @default(autoincrement())
  uuid                     String?                    @unique
  additional_info          user_additional_info?      @relation("UserAdditionalInfoRelation")
  registered_on            DateTime?                  @db.Timestamp(0)
  starred                  Boolean?                   @default(false)
  type                     String?                    @db.VarChar(100)
  deleted                  Boolean?                   @default(false)
  valid_until              DateTime?                  // Add this line
  approval_emails_child    approval_emails[]          @relation("approval_emails_childUserIdTouser")
  approval_emails_guardian approval_emails[]          @relation("approval_emails_guardianUserIdTouser")
  parent_info_id           Int?
  parent_info              parent_info?               @relation("UserParentInfo", fields: [parent_info_id], references: [id])
  guardians                user_guardian[]            @relation("UserToGuardian")
  children                 user_guardian[]            @relation("GuardianToUser")
  calibration              calibration[]
  comments                 comments[]
  scheduled_test           scheduled_test[]
  sub_admin_assigned_users sub_admin_assigned_users[] @ignore
  training_session_data    training_session_data[]    @relation("UserTrainingSessionData") // Only reference to training_session_data
  vergence_user_session    vergence_user_session[]
  userSurvey               userSurvey[]
  student                  student?
  activations              activation[]               @relation("UserToActivation")
  training_session         training_session[]
  device_data device_data[] @relation("UserDevice")
  training_config          training_config[]
}






model user_additional_info {
  id                    Int       @id @default(autoincrement())
  name                  String?   @db.VarChar(200)
  email                 String?   @db.VarChar(200)
  age                   Int?
  vision_problem        String?   @db.VarChar(200)
  optional_text         String?   @db.Text
  accept_newsletter     Boolean?  @default(false)
  user_id               Int       @unique
  birthdate             DateTime?
  first_name            String?   @db.VarChar(200)
  last_name             String?   @db.VarChar(200)
  notification_hour     Int?
  training_question_id  Int?      @default(1)
  change_flag                Boolean?  @default(false)
  read_calibration_from_backend Boolean? @default(false) // New field
  performed_test_today                Boolean?  @default(false)
  has_double_vision                Boolean?  @default(false)
  starred                      Boolean?  @default(false) 

  user                       user      @relation("UserAdditionalInfoRelation", fields: [user_id], references: [id], onUpdate: Restrict)

  @@index([user_id], map: "user_additional_info_user_fk")
}



model parent_info {
  id         Int     @id @default(autoincrement())
  first_name String? @db.VarChar(255)
  last_name  String? @db.VarChar(255)
  email      String? @unique @db.VarChar(255)
  users      user[]  @relation("UserParentInfo")
}

model user_guardian {
  id          Int  @id @default(autoincrement())
  user_id     Int
  guardian_id Int
  guardian    user @relation("UserToGuardian", fields: [guardian_id], references: [id])
  user        user @relation("GuardianToUser", fields: [user_id], references: [id])

  @@index([guardian_id], map: "user_guardian_guardian_id_fkey")
  @@index([user_id], map: "user_guardian_user_id_fkey")
}

model vergence_answer {
  id                   Int                    @id @default(autoincrement())
  vergence_question_id Int
  deleted              Boolean?               @default(false)
  answer               String?                @db.VarChar(100)
  value                Int?
  vergence_question    vergence_question      @relation(fields: [vergence_question_id], references: [id], onUpdate: Restrict, map: "vergence_answer_question_fk")
  vergence_user_answer vergence_user_answer[]

  @@index([vergence_question_id], map: "vergence_answer_question_fk")
}

model vergence_question {
  id              Int               @id @default(autoincrement())
  title           String?           @db.VarChar(500)
  language        String?           @db.VarChar(20)
  deleted         Boolean?          @default(false)
  sequence        Int?              @db.SmallInt
  answers vergence_answer[]
}

model vergence_user_answer {
  id                    Int                    @id @default(autoincrement())
  session_id            Int?
  answer_id             Int?
  deleted               Boolean?
  answer       vergence_answer?       @relation(fields: [answer_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "vergence_user_answer_vergence_answer_fk")
  vergence_user_session vergence_user_session? @relation(fields: [session_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "vergence_user_answer_vergence_user_session_fk")

  @@index([answer_id], map: "vergence_user_answer_vergence_answer_fk")
  @@index([session_id], map: "vergence_user_answer_vergence_user_session_fk")
}

model vergence_user_session {
  id                      Int                    @id @default(autoincrement())
  user_id                 Int?
  implemented_by_guardian Boolean?               @default(false)
  created_at              DateTime?              @db.DateTime(0)
  deleted                 Boolean?
  vergence_user_answer    vergence_user_answer[]
  user                    user?                  @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "vergence_user_session_user__fk")

  @@index([user_id], map: "vergence_user_session_user__fk")
}

model pdMeasurement {
  id              Int      @id @default(autoincrement())
  optician_number Float
  measured_number Float

  @@map("pd_measurement") 
}

model postTrainingData {
  id          Int   @id @default(autoincrement())
  user_id     Int
  question_id Int
  question    String @db.VarChar(100)
  answer_id   Int
  answer      String @db.VarChar(100)
  created_at  DateTime @default(now())

  @@map("training_questions_data")
}

model Purchase {
  id                   Int            @id @default(autoincrement())
  email                String
  code                 String         @unique
  created_at           DateTime       @default(now())
  duration             Int
  first_name           String
  is_subscription      Boolean
  last_name            String
  number_of_licenses   Int
  number_of_vr_glasses Int
  order_number         String
  updated_at           DateTime       @updatedAt
  activations          activation[]
  students             student[]      @relation("PurchaseToStudents")
  shipping_Info        shipping_info? @relation("PurchaseToShipping")
  orderStatus          orderStatus?   @relation("PurchaseToOrderStatus")
  additional_info      purchase_additional_info[]


  @@map("purchase")
}

model activation {
  id              Int      @id @default(autoincrement())
  purchase_id     Int
  activation_date DateTime @default(now())
  updated_at      DateTime @updatedAt
  user_id         Int?
  purchase        Purchase @relation(fields: [purchase_id], references: [id], onUpdate: Restrict, map: "activation_purchase__fk")
  user            user?    @relation("UserToActivation", fields: [user_id], references: [id])

  @@index([purchase_id], map: "activation_purchase__fk")
  @@index([user_id], map: "purchase_activation_user_id_fkey")
  @@map("purchase_activation")
}

model userSurvey {
  id             Int  @id @default(autoincrement())
  user_id        Int
  question_index Int
  answer_index   Int
  user           user @relation(fields: [user_id], references: [id])

  @@index([user_id], map: "user_survey_user_id_fkey")
  @@map("user_survey")
}

model school {
  id                Int                 @id @default(autoincrement())
  name              String
  city              String
  binogiPermissions binogiPermissions[] @relation("SchoolToPermissions")
  classes           schoolClass[]       @relation("SchoolToClasses")

  @@map("school")
}

model binogiPermissions {
  id                Int    @id @default(autoincrement())
  school_id         Int
  binogi_section_id Int
  school            school @relation("SchoolToPermissions", fields: [school_id], references: [id])

  @@index([school_id], map: "binogi_permissions_school_id_fkey")
  @@map("binogi_permissions")
}

model schoolClass {
  id       Int       @id @default(autoincrement())
  name     String
  schoolId Int       @map("school_id")
  school   school    @relation("SchoolToClasses", fields: [schoolId], references: [id])
  students student[]

  @@index([schoolId], map: "school_class_school_id_fkey")
  @@map("school_class")
}

model teacher {
  id               Int              @id @default(autoincrement())
  studentTeachers  studentTeacher[]
  teacher_uuid     String           @unique
  first_name       String
  last_name        String
  role             String?
  report_frequency Int?             @default(0)
}

model student {
  id              Int              @id @default(autoincrement())
  userId          Int?             @unique @map("user_id")
  first_name      String?
  last_name       String?
  birthdate       DateTime?
  email           String?          @unique
  purchase_id     Int?
  classId         Int?             @map("class_id")
  class           schoolClass?     @relation(fields: [classId], references: [id])
  purchase        Purchase?        @relation("PurchaseToStudents", fields: [purchase_id], references: [id])
  user            user?            @relation(fields: [userId], references: [id])
  studentTeachers studentTeacher[]

  @@index([classId], map: "student_class_id_fkey")
  @@index([purchase_id], map: "student_purchase_id_fkey")
  @@map("student")
}

model studentTeacher {
  id        Int     @id @default(autoincrement())
  studentId Int     @map("student_id")
  teacherId Int     @map("teacher_id")
  student   student @relation(fields: [studentId], references: [id])
  teacher   teacher @relation(fields: [teacherId], references: [id])

  @@index([studentId], map: "studentId_idx")
  @@index([teacherId], map: "teacherId_idx")
  @@map("student_teacher")
}
model training_session_data {
  id               Int      @id @default(autoincrement())
  user_id          Int?
  session_number   Int
  session_duration Int
  start_time       DateTime
  type             String?
  streamingSource  String?
  speed            Float?
  pendlumLength    Float?
  offset           Float?
  oscillationTime  Float?
  
  deviceId         Int?     @map("device_id")
  device           device_data? @relation("DeviceTrainingSessions", fields: [deviceId], references: [id])
  
  // Relation to user with relation name "UserTrainingSessionData"
  user             user?    @relation("UserTrainingSessionData", fields: [user_id], references: [id])
  
  @@index([user_id], map: "user_id")
}


model shipping_info {
  id              Int      @id @default(autoincrement())
  purchase_id     Int      @unique
  shipping_date   DateTime @default(now())
  tracking_number String
  purchase        Purchase @relation("PurchaseToShipping", fields: [purchase_id], references: [id])

  @@index([purchase_id], map: "purchase_shipping_info_purchase_id_fkey")
  @@map("purchase_shipping_info")
}

model orderStatus {
  id          Int      @id @default(autoincrement())
  order_id    String    @unique
  purchase_id Int?      @unique
  status      String
  purchase    Purchase? @relation("PurchaseToOrderStatus", fields: [purchase_id], references: [id])

  @@index([purchase_id], map: "purchase_order_status_purchase_id_fkey")
  @@map("purchase_order_status")
}

enum UserAdminRole {
  SUPERADMIN
  ADMIN
  SUBADMIN
  EDITOR
  TEACHER
  USER
  MARKETING
  DEVELOPER
}


model account {
  id                 String  @id @default(cuid())
  userId             String  @unique @map("user_id")
  role               UserAdminRole @default(USER)
  provider           String
  providerAccountId  String  @map("provider_account_id")
  refresh_token      String? @db.Text
  access_token       String? @db.Text
  expires_at         Int?
  token_type         String?
  scope              String?
  id_token           String? @db.Text
  session_state      String?
  refresh_token_expires_in Int?
  user user_login @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
 
  @@unique([provider, providerAccountId])
  @@map("admin_accounts")
}


 
model user_login {
  id                String    @id @default(cuid())
  name              String?
  email             String?   @unique
  emailVerified     DateTime?
  password          String?   @db.VarChar(250)
  role              UserAdminRole @default(USER)
  accounts          account[]
  sessions          login_session[]
  authenticator     authenticator[]
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  activities        admin_activity[]
 
  @@map("admin_user")
}
 
model login_session {
  id           String   @id @default(cuid())
  sessionToken String   @unique @map("session_token")
  userId       String   @map("user_id")
  expires      DateTime
  user         user_login     @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([expires])
  @@map("login_sessions")
}

model verification_token {
  id         String   @id @default(cuid())
  identifier String
  token      String
  expires    DateTime
  createdAt  DateTime @default(now())
 
  @@unique([identifier, token])
}

model authenticator {
  credentialID         String  @unique
  userId               String
  providerAccountId    String
  credentialPublicKey  String
  counter              Int
  credentialDeviceType String
  credentialBackedUp   Boolean
  transports           String?
  user user_login @relation(fields: [userId], references: [id], onDelete: Cascade)
 
  @@id([userId, credentialID])
}

enum PurchaseType {
  DEFAULT
  SUBSCRIPTION
  START_PACKAGE
  CONTINUE_TRAINING
}

enum PurchaseSource {
  DEFAULT
  ADMIN
  WEBSHOP
  IMPORTED
}

model purchase_additional_info {
  id              Int      @id @default(autoincrement())
  purchase_id     Int
  info            String?  @db.Text
  is_hidden       Boolean? @default(false)
  purchase_type   PurchaseType? @default(DEFAULT)  
  purchase_source PurchaseSource? @default(IMPORTED)
  
  // New address and order fields
  address_line    String?  @db.VarChar(255)
  city            String?  @db.VarChar(100)
  state           String?  @db.VarChar(100)
  postal_code     String?  @db.VarChar(20)
  country         String?  @db.VarChar(100)
  phone           String?  @db.VarChar(50)
  order_amount    Decimal? @db.Decimal(10, 2)

  purchase    Purchase @relation(fields: [purchase_id], references: [id])

  @@index([purchase_id])
  @@map("purchase_additional_info")
}

model admin_activity {
  id          Int       @id @default(autoincrement())
  admin_id    String?    // References user_login.id
  action      String    @db.VarChar(255)
  entity      String?   @db.VarChar(255) // The type of resource being affected (e.g., "user", "purchase")
  entity_id   String?   // The ID of the affected resource
  details     Json?     // Flexible JSON field for additional details
  ip_address  String?   @db.VarChar(45)
  created_at  DateTime  @default(now())
  admin       user_login? @relation(fields: [admin_id], references: [id])

  @@index([admin_id])
  @@index([created_at])
  @@map("admin_activities")
}

model training_config {
  id          Int      @id @default(autoincrement())
  user_id     Int
  
  left_eye_offset_x  Float?
  left_eye_offset_y  Float?
  left_eye_speed_x   Float?
  left_eye_speed_y   Float?
  left_eye_pendulum_x Float?
  left_eye_pendulum_y Float?

  right_eye_offset_x  Float?
  right_eye_offset_y  Float?
  right_eye_speed_x   Float?
  right_eye_speed_y   Float?
  right_eye_pendulum_x Float?
  right_eye_pendulum_y Float?

  created_at  DateTime @default(now())
  user        user     @relation(fields: [user_id], references: [id])

  @@index([user_id], map: "training_config_user_id_fkey")
}

model ReadingLetter {
  id       Int    @id @default(autoincrement())
  value    String
  language String

  @@map("reading_letters")
}

model ReadingWord {
  id       Int    @id @default(autoincrement())
  value    String
  language String

  @@map("reading_words")
}

model ReadingSentence {
  id       Int    @id @default(autoincrement())
  value    String
  language String

  @@map("reading_sentences")
}
model ReadingSentencesChain {
  id               Int      @id @default(autoincrement())
  sentenceId       Int      @map("sentence_id")
  language         String
  sentence         String
  correctPosition1 Int      @map("correct_position_1")
  correctPosition2 Int      @map("correct_position_2")

  // One-to-many relations
  correctTimes ReadingSentencesChainCorrectTime[]
  wrongTimes   ReadingSentencesChainWrongTime[]

  @@map("reading_sentences_chain")
}

model ReadingSentencesChainCorrectTime {
  id      Int    @id @default(autoincrement())
  chainId Int    @map("chain_id")
  time    Float

  chain   ReadingSentencesChain @relation(fields: [chainId], references: [id])

  @@map("reading_sentences_chain_correct_times")
}

model ReadingSentencesChainWrongTime {
  id      Int    @id @default(autoincrement())
  chainId Int    @map("chain_id")
  time    Float

  chain   ReadingSentencesChain @relation(fields: [chainId], references: [id])

  @@map("reading_sentences_chain_wrong_times")
}

model ReadingLettersChain {
  id               Int    @id @default(autoincrement())
  letterId         Int    @map("letter_id")
  language         String
  letter           String
  correctPosition1 Int    @map("correct_position_1")
  correctPosition2 Int    @map("correct_position_2")

  // Relations to correct/wrong times
  correctTimes ReadingLettersChainCorrectTime[]
  wrongTimes   ReadingLettersChainWrongTime[]

  @@map("reading_letters_chain")
}

model ReadingLettersChainCorrectTime {
  id      Int    @id @default(autoincrement())
  chainId Int    @map("chain_id")
  time    Float

  chain   ReadingLettersChain @relation(fields: [chainId], references: [id])

  @@map("reading_letters_chain_correct_times")
}

model ReadingLettersChainWrongTime {
  id      Int    @id @default(autoincrement())
  chainId Int    @map("chain_id")
  time    Float

  chain   ReadingLettersChain @relation(fields: [chainId], references: [id])

  @@map("reading_letters_chain_wrong_times")
}
model ReadingWordsChain {
  id               Int    @id @default(autoincrement())
  wordId           Int    @map("word_id")
  language         String
  word             String
  correctPosition1 Int    @map("correct_position_1")
  correctPosition2 Int    @map("correct_position_2")

  correctTimes ReadingWordsChainCorrectTime[]
  wrongTimes   ReadingWordsChainWrongTime[]

  @@map("reading_words_chain")
}

model ReadingWordsChainCorrectTime {
  id      Int    @id @default(autoincrement())
  chainId Int    @map("chain_id")
  time    Float

  chain   ReadingWordsChain @relation(fields: [chainId], references: [id])

  @@map("reading_words_chain_correct_times")
}

model ReadingWordsChainWrongTime {
  id      Int    @id @default(autoincrement())
  chainId Int    @map("chain_id")
  time    Float

  chain   ReadingWordsChain @relation(fields: [chainId], references: [id])

  @@map("reading_words_chain_wrong_times")
}

model ReadingTestData {
  id       Int    @id @default(autoincrement())
  userId   Int    @map("user_id")
  testDate DateTime @map("test_date")
  language String
  testType String   @map("test_type")
  score    Int

  userCorrectTimes  UserCorrectAnswerTime[]
  userWrongTimes    UserWrongAnswerTime[]

  @@map("reading_test_data")
}

model UserCorrectAnswerTime {
  id         Int    @id @default(autoincrement())
  testDataId Int    @map("test_data_id")
  chainId    Int    @map("chain_id")
  time       String
  value      Int

  readingTestData ReadingTestData @relation(fields: [testDataId], references: [id])

  @@map("user_correct_answer_times")
}

model UserWrongAnswerTime {
  id         Int    @id @default(autoincrement())
  testDataId Int    @map("test_data_id")
  chainId    Int    @map("chain_id")
  time       String
  value      Int

  readingTestData ReadingTestData @relation(fields: [testDataId], references: [id])

  @@map("user_wrong_answer_times")
}

enum NotificationType {
  SHIPPING_MISSING
  PURCHASE_STATUS
  TRAINING_REMINDER
  TODO_REMINDER
}

model admin_notifications {
  id          Int   @id @default(autoincrement())
  title       String @db.VarChar(255)
  message     String
  type        NotificationType
  read        Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  metadata    Json?    // For additional data
}

enum TodoStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum TodoPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

model admin_todos {
  id              Int         @id @default(autoincrement())
  title           String      @db.VarChar(255)
  description     String?     @db.Text
  status          TodoStatus  @default(PENDING)
  priority        TodoPriority @default(MEDIUM)
  due_date        DateTime?
  reminder_time   DateTime?
  completed_at    DateTime?
  created_by      String      // References admin user ID
  created_by_name String?     @db.VarChar(255) // Name of the creator
  assigned_to     String?     // References admin user ID
  assigned_to_name String?    @db.VarChar(255) // Name of the assignee
  updated_by      String?     // References admin user ID who last updated
  updated_by_name String?     @db.VarChar(255) // Name of the last updater
  tags            Json?       // Array of tags
  metadata        Json?       // Additional data
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  @@index([created_by])
  @@index([assigned_to])
  @@index([status])
  @@index([due_date])
  @@index([reminder_time])
}



