# Admin User Management Best Practices

## Overview

This document outlines the best practices for managing admin users and implementing secure role-based access control for todo assignment functionality.

## Security Principles

### 1. Role-Based Access Control (RBAC)

The system implements a hierarchical role structure using the `UserAdminRole` enum:

```typescript
enum UserAdminRole {
  SUPERADMIN    // Highest level - can manage all users and roles
  ADMIN         // Can manage most admin functions except SUPERADMIN
  SUBADMIN      // Limited admin capabilities
  TEACHER       // Educational content focused
  USER          // Standard user - no admin privileges
  MARKETING     // Marketing focused admin
  DEVELOPER     // Technical admin with development access
}
```

### 2. Permission Hierarchy

**SUPERADMIN** can assign todos to:
- SUPERADMIN, ADMIN, SUBADMIN, DE<PERSON><PERSON>OPER, <PERSON>R<PERSON>TING

**ADMIN** can assign todos to:
- ADMIN, SUBADMIN, <PERSON><PERSON><PERSON>OPER, MARKETING (excludes SUPERADMIN)

**DEVELOPER** can assign todos to:
- <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>MI<PERSON> (technical roles only)

**Other roles** cannot access admin user lists for security reasons.

## API Endpoint

### GET /auth_admin/admin-users

**Purpose**: Retrieve list of admin users for todo assignment

**Authentication**: Required (Bearer token)

**Authorization**: Only accessible by ADMIN, SUPERADMIN, or DEVELOPER roles

**Response Format**:
```json
{
  "success": true,
  "data": [
    {
      "id": "user_id_string",
      "name": "User Name",
      "email": "<EMAIL>",
      "role": "ADMIN"
    }
  ],
  "message": "Admin users retrieved successfully"
}
```

**Error Responses**:
- `401 Unauthorized`: Insufficient permissions or invalid token
- `500 Internal Server Error`: Server-side error

## Implementation Details

### Service Method: `getAdminUsers()`

Located in `src/auth_admin/adminAuth.service.ts`

**Features**:
- Role-based filtering of assignable users
- Only returns verified users (emailVerified is not null)
- Ordered by role hierarchy, then by name
- Comprehensive error handling
- Activity logging for audit trails

### Security Measures

1. **Permission Validation**: Checks requesting user's role before processing
2. **Data Filtering**: Only returns users that the requesting user can assign todos to
3. **Verified Users Only**: Excludes unverified accounts from the list
4. **Activity Logging**: Logs all access attempts for security auditing
5. **Error Handling**: Prevents information leakage through error messages

## Usage Examples

### Frontend Integration

```typescript
// Example API call from frontend
const getAdminUsers = async () => {
  try {
    const response = await fetch('/auth_admin/admin-users', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch admin users');
    }
    
    const data = await response.json();
    return data.data; // Array of admin users
  } catch (error) {
    console.error('Error fetching admin users:', error);
    throw error;
  }
};
```

### Todo Assignment Integration

```typescript
// Example usage in todo creation/assignment
const createTodoWithAssignment = async (todoData) => {
  // First get available admin users
  const adminUsers = await getAdminUsers();
  
  // Show admin users in dropdown/selection component
  // User selects an admin user
  const selectedAdminId = userSelection;
  
  // Create todo with assignment
  const todo = await createTodo({
    ...todoData,
    assigned_to: selectedAdminId
  });
  
  return todo;
};
```

## Best Practices for Frontend Implementation

### 1. Conditional Rendering
Only show admin user selection to users with appropriate roles:

```typescript
const canAssignTodos = ['ADMIN', 'SUPERADMIN', 'DEVELOPER'].includes(userRole);

return (
  <div>
    {canAssignTodos && (
      <AdminUserSelector onSelect={handleAdminSelection} />
    )}
  </div>
);
```

### 2. Error Handling
Implement proper error handling for permission issues:

```typescript
const handleGetAdminUsers = async () => {
  try {
    const users = await getAdminUsers();
    setAdminUsers(users);
  } catch (error) {
    if (error.status === 401) {
      showError('You do not have permission to assign todos to other users');
    } else {
      showError('Failed to load admin users');
    }
  }
};
```

### 3. Caching Strategy
Cache admin user list to reduce API calls:

```typescript
const useAdminUsers = () => {
  const [adminUsers, setAdminUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    const fetchAdminUsers = async () => {
      setLoading(true);
      try {
        const users = await getAdminUsers();
        setAdminUsers(users);
        // Cache for 5 minutes
        localStorage.setItem('adminUsers', JSON.stringify({
          data: users,
          timestamp: Date.now()
        }));
      } catch (error) {
        console.error('Failed to fetch admin users:', error);
      } finally {
        setLoading(false);
      }
    };
    
    // Check cache first
    const cached = localStorage.getItem('adminUsers');
    if (cached) {
      const { data, timestamp } = JSON.parse(cached);
      if (Date.now() - timestamp < 5 * 60 * 1000) { // 5 minutes
        setAdminUsers(data);
        return;
      }
    }
    
    fetchAdminUsers();
  }, []);
  
  return { adminUsers, loading };
};
```

## Security Considerations

1. **Never expose user lists to unauthorized roles**
2. **Always validate permissions on both frontend and backend**
3. **Log all access attempts for security auditing**
4. **Use HTTPS for all API communications**
5. **Implement rate limiting to prevent abuse**
6. **Regularly audit user roles and permissions**

## Testing

### Unit Tests
Test the service method with different user roles:

```typescript
describe('AdminAuthService.getAdminUsers', () => {
  it('should return admin users for SUPERADMIN', async () => {
    const result = await service.getAdminUsers(UserAdminRole.SUPERADMIN);
    expect(result).toBeDefined();
    expect(result.length).toBeGreaterThan(0);
  });
  
  it('should throw UnauthorizedException for USER role', async () => {
    await expect(
      service.getAdminUsers(UserAdminRole.USER)
    ).rejects.toThrow(UnauthorizedException);
  });
});
```

### Integration Tests
Test the API endpoint with different authentication scenarios:

```typescript
describe('GET /auth_admin/admin-users', () => {
  it('should return admin users for authenticated admin', async () => {
    const response = await request(app)
      .get('/auth_admin/admin-users')
      .set('Authorization', `Bearer ${adminToken}`)
      .expect(200);
      
    expect(response.body.success).toBe(true);
    expect(response.body.data).toBeInstanceOf(Array);
  });
  
  it('should return 401 for non-admin user', async () => {
    await request(app)
      .get('/auth_admin/admin-users')
      .set('Authorization', `Bearer ${userToken}`)
      .expect(401);
  });
});
```

## Monitoring and Auditing

The system automatically logs all admin user access attempts through the `AdminActivitiesService`. Monitor these logs for:

- Unusual access patterns
- Failed permission attempts
- Bulk data access
- Access from unexpected IP addresses

## Conclusion

This implementation provides a secure, role-based approach to admin user management that prevents unauthorized access while maintaining flexibility for legitimate administrative tasks. Always follow the principle of least privilege and regularly audit user permissions.
